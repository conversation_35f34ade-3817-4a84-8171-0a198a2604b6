import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';

// 获取用户列表
export async function GET(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    // 获取用户列表
    const { data: users, error } = await supabaseAdmin
      .from('users')
      .select('id, username, email, is_active, is_admin, created_at, updated_at, last_login')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '获取用户列表失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      data: users
    });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 更新用户状态
export async function PATCH(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const body = await request.json();
    const { userId, is_active } = body;

    if (!userId || typeof is_active !== 'boolean') {
      return NextResponse.json({
        success: false,
        message: '参数不正确'
      }, { status: 400 });
    }

    // 更新用户状态
    const { error } = await supabaseAdmin
      .from('users')
      .update({ 
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '更新用户状态失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: `用户已${is_active ? '启用' : '禁用'}`
    });

  } catch (error) {
    console.error('Update user error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 删除用户
export async function DELETE(request: NextRequest) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId');

    if (!userId) {
      return NextResponse.json({
        success: false,
        message: '用户ID不能为空'
      }, { status: 400 });
    }

    // 删除用户相关的所有数据
    await supabaseAdmin.from('token_usage').delete().eq('user_id', userId);
    await supabaseAdmin.from('comments').delete().eq('user_id', userId);
    
    // 删除用户
    const { error } = await supabaseAdmin
      .from('users')
      .delete()
      .eq('id', userId);

    if (error) {
      console.error('Database error:', error);
      return NextResponse.json({
        success: false,
        message: '删除用户失败'
      }, { status: 500 });
    }

    return NextResponse.json({
      success: true,
      message: '用户已删除'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}
