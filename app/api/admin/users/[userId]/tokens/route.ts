import { NextRequest, NextResponse } from 'next/server';
import { supabaseAdmin } from '@/lib/supabase';
import { verifyToken, extractTokenFromHeader } from '@/lib/auth';
import { UserTokenStats } from '@/types';

// 获取单个用户的Token使用统计
export async function GET(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    // 验证管理员身份
    const authHeader = request.headers.get('authorization');
    const token = extractTokenFromHeader(authHeader);
    
    if (!token) {
      return NextResponse.json({
        success: false,
        message: '未提供认证令牌'
      }, { status: 401 });
    }

    const payload = verifyToken(token);
    if (!payload || !payload.isAdmin) {
      return NextResponse.json({
        success: false,
        message: '权限不足'
      }, { status: 403 });
    }

    const userId = params.userId;

    // 获取用户基本信息
    const { data: user, error: userError } = await supabaseAdmin
      .from('users')
      .select('id, username, token_limit')
      .eq('id', userId)
      .single();

    if (userError || !user) {
      return NextResponse.json({
        success: false,
        message: '用户不存在'
      }, { status: 404 });
    }

    // 获取用户总Token使用量
    const { data: tokenUsageData, error: tokenError } = await supabaseAdmin
      .from('token_usage')
      .select('tokens_used, api_call_type, created_at')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (tokenError) {
      console.error('获取Token使用记录失败:', tokenError);
      return NextResponse.json({
        success: false,
        message: '获取Token使用记录失败'
      }, { status: 500 });
    }

    // 计算总使用量
    const totalTokensUsed = tokenUsageData?.reduce((sum, record) => sum + record.tokens_used, 0) || 0;

    // 计算使用百分比
    const usagePercentage = user.token_limit 
      ? Math.round((totalTokensUsed / user.token_limit) * 100)
      : 0;

    // 获取最近10条使用记录
    const recentUsage = tokenUsageData?.slice(0, 10) || [];

    // 计算每日使用量（最近7天）
    const dailyUsage = calculateDailyUsage(tokenUsageData || []);

    const stats: UserTokenStats = {
      user_id: userId,
      username: user.username,
      total_tokens_used: totalTokensUsed,
      token_limit: user.token_limit,
      usage_percentage: usagePercentage,
      recent_usage: recentUsage,
      daily_usage: dailyUsage
    };

    return NextResponse.json({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取用户Token统计失败:', error);
    return NextResponse.json({
      success: false,
      message: '服务器内部错误'
    }, { status: 500 });
  }
}

// 计算每日使用量
function calculateDailyUsage(tokenUsageData: any[]): { date: string; tokens: number }[] {
  const dailyMap = new Map<string, number>();
  
  // 初始化最近7天的数据
  for (let i = 6; i >= 0; i--) {
    const date = new Date();
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    dailyMap.set(dateStr, 0);
  }

  // 统计每日使用量
  tokenUsageData.forEach(record => {
    const date = new Date(record.created_at).toISOString().split('T')[0];
    if (dailyMap.has(date)) {
      dailyMap.set(date, (dailyMap.get(date) || 0) + record.tokens_used);
    }
  });

  // 转换为数组格式
  return Array.from(dailyMap.entries()).map(([date, tokens]) => ({
    date,
    tokens
  }));
}
