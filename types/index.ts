// 用户相关类型
export interface User {
  id: string;
  username: string;
  email: string;
  is_active: boolean;
  is_admin: boolean;
  created_at: string;
  updated_at: string;
  token_limit?: number; // Token使用限制，null表示无限制
  last_login?: string;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  token?: string;
  user?: User;
}

// 学生信息相关类型
export interface StudentInfo {
  // 基本信息
  studentName: string;
  studentGender: '男' | '女';
  termYear: '2023-2024学年第一学期' | '2023-2024学年第二学期' | '2024-2025学年第一学期' | '2024-2025学年第二学期';
  
  // 学业成绩与发展
  majorSubjectPerformance: string;
  subjectStrengths: string;
  subjectWeaknesses: string;
  learningPotential: '潜力较大' | '学习能力稳定' | '需要进一步激发潜力';
  subjectInterest: '浓厚' | '一般' | '缺乏兴趣';
  
  // 课堂表现与参与
  classroomConcentration: '始终高度专注' | '大部分时间专注' | '有时分散注意力' | '容易受外界干扰';
  
  // 行为习惯与态度
  homeworkCompletion: '总是按时高质量' | '大部分按时完成' | '有时拖延或应付';
  learningProactiveness: '学习主动性强' | '完成要求任务' | '需要督促';
  disciplineCompliance: '严格遵守' | '基本遵守' | '需要加强';
  attitudeTowardsOthers: '有礼貌尊重他人' | '基本有礼貌' | '不够尊重';
  responsibility: '较强' | '能完成分配任务' | '有待提高';
  
  // 个性特长与潜能
  talentsAndInterests: string;
  
  // 教师期望与个性化建议
  overallAssessment: string;
  futureExpectations: string;
  improvementSuggestions: string;
  
  // 评语设置
  commentPerspective: '你' | '该生';
  wordCountRange: string;
}

// 评语生成相关类型
export interface CommentGenerationRequest {
  studentInfo: StudentInfo;
}

export interface CommentGenerationResponse {
  success: boolean;
  message: string;
  comment?: string;
  tokensUsed?: number;
}

// Token使用记录类型
export interface TokenUsage {
  id: string;
  user_id: string;
  tokens_used: number;
  api_call_type: string;
  request_data?: any;
  response_data?: any;
  created_at: string;
}

// 用户Token统计类型
export interface UserTokenStats {
  user_id: string;
  username: string;
  total_tokens_used: number;
  token_limit: number | null;
  usage_percentage: number;
  recent_usage: TokenUsage[];
  daily_usage: { date: string; tokens: number }[];
}

// Token限制设置请求类型
export interface TokenLimitRequest {
  userId: string;
  tokenLimit: number | null; // null表示无限制
}

// 管理员统计类型
export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalTokensUsed: number;
  totalComments: number;
}
